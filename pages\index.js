import { useRouter } from "next/router";
import Layout from "../components/Layout";
import { ArrowRight, Users, Briefcase, Shield, Star } from "lucide-react";

export default function Home({ user }) {
  const router = useRouter();

  return (
    <Layout user={user}>
      <div className="min-h-screen bg-white">
        {/* Hero Section */}
        <section className="px-4 py-20 mx-auto max-w-7xl">
          <div className="text-center">
            <h1 className="text-5xl md:text-7xl font-bold mb-6">
              <span className="gradient-text">Split Job</span>
            </h1>
            <p className="text-2xl md:text-3xl text-gray-600 mb-8">
              Your Job, Our Salary.
            </p>
            <p className="text-lg md:text-xl text-gray-500 max-w-2xl mx-auto mb-12">
              Connect with the right people. Share success. Get hired faster
              with our innovative referral system.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={() => router.push("/signup")}
                className="px-8 py-4 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors flex items-center justify-center gap-2"
              >
                Get Started <ArrowRight size={20} />
              </button>
              <button
                onClick={() => router.push("/explore")}
                className="px-8 py-4 border-2 border-black rounded-lg hover:bg-gray-100 transition-colors"
              >
                Explore Opportunities
              </button>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="px-4 py-20 bg-gray-50">
          <div className="max-w-7xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center mb-16">
              How It Works
            </h2>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="text-center p-6">
                <div className="w-16 h-16 bg-black rounded-full flex items-center justify-center mx-auto mb-6">
                  <Users className="text-white" size={32} />
                </div>
                <h3 className="text-xl font-semibold mb-4">Connect</h3>
                <p className="text-gray-600">
                  Job seekers post opportunities with reward percentages.
                  Referrers browse and apply to help.
                </p>
              </div>
              <div className="text-center p-6">
                <div className="w-16 h-16 bg-black rounded-full flex items-center justify-center mx-auto mb-6">
                  <Shield className="text-white" size={32} />
                </div>
                <h3 className="text-xl font-semibold mb-4">Secure</h3>
                <p className="text-gray-600">
                  Both parties sign contracts and exchange verified documents
                  before any referral.
                </p>
              </div>
              <div className="text-center p-6">
                <div className="w-16 h-16 bg-black rounded-full flex items-center justify-center mx-auto mb-6">
                  <Star className="text-white" size={32} />
                </div>
                <h3 className="text-xl font-semibold mb-4">Succeed</h3>
                <p className="text-gray-600">
                  Once hired, both parties fulfill the agreement and build their
                  reputation with reviews.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="px-4 py-20 bg-white">
          <div className="max-w-7xl mx-auto">
            <div className="grid md:grid-cols-2 gap-16 items-center">
              <div>
                <h2 className="text-3xl md:text-4xl font-bold mb-6">
                  Why Choose Split Job?
                </h2>
                <ul className="space-y-4">
                  <li className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-black rounded-full mt-0.5"></div>
                    <div>
                      <h4 className="font-semibold">
                        Flexible Payment Options
                      </h4>
                      <p className="text-gray-600">
                        Choose between percentage-based or fixed amount rewards
                      </p>
                    </div>
                  </li>
                </ul>
              </div>
              <div className="bg-gray-100 rounded-2xl p-8">
                <div className="space-y-6">
                  <div>
                    <div className="text-4xl font-bold">10K+</div>
                    <div className="text-gray-600">Active Users</div>
                  </div>
                  <div>
                    <div className="text-4xl font-bold">2.5K+</div>
                    <div className="text-gray-600">Successful Placements</div>
                  </div>
                  <div>
                    <div className="text-4xl font-bold">₹50L+</div>
                    <div className="text-gray-600">Rewards Distributed</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="px-4 py-20 bg-black text-white">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Ready to Split Your Success?
            </h2>
            <p className="text-xl mb-8 text-gray-300">
              Join thousands of job seekers and referrers already using Split
              Job
            </p>
            <button
              onClick={() => router.push("/signup")}
              className="px-8 py-4 bg-white text-black rounded-lg hover:bg-gray-200 transition-colors"
            >
              Join Now - It's Free
            </button>
          </div>
        </section>
      </div>
    </Layout>
  );
}
