import { withAuth } from "../../../lib/middleware";
import { query } from "../../../lib/db";

export default withAuth(async (req, res) => {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  const { contractId, action } = req.body;

  if (!contractId || !action) {
    return res.status(400).json({ error: "Contract ID and action are required" });
  }

  try {
    // Get contract details
    const contracts = await query(
      `
      SELECT 
        c.*,
        a.referrer_id,
        jp.user_id as seeker_id
      FROM contracts c
      JOIN applications a ON c.application_id = a.id
      JOIN job_posts jp ON a.job_post_id = jp.id
      WHERE c.id = ?
    `,
      [contractId]
    );

    if (contracts.length === 0) {
      return res.status(404).json({ error: "Contract not found" });
    }

    const contract = contracts[0];

    // Determine which party is verifying
    let updateField;
    if (req.user.id === contract.seeker_id) {
      updateField = "seeker";
    } else if (req.user.id === contract.referrer_id) {
      updateField = "referrer";
    } else {
      return res
        .status(403)
        .json({ error: "Unauthorized to verify documents for this contract" });
    }

    if (action === "verify") {
      // Update document verification status
      await query(
        `UPDATE contracts 
         SET ${updateField}_documents_verified = TRUE, ${updateField}_verified_at = NOW() 
         WHERE id = ?`,
        [contractId]
      );

      // Check if both parties have verified documents
      const updatedContracts = await query(
        "SELECT seeker_documents_verified, referrer_documents_verified FROM contracts WHERE id = ?",
        [contractId]
      );

      let newStatus = "documents_pending";
      if (
        updatedContracts[0].seeker_documents_verified &&
        updatedContracts[0].referrer_documents_verified
      ) {
        newStatus = "documents_verified";
        
        // Update contract status
        await query('UPDATE contracts SET status = ? WHERE id = ?', [
          newStatus,
          contractId,
        ]);
      }

      res.status(200).json({
        success: true,
        message: "Documents verified successfully",
        bothVerified: updatedContracts[0].seeker_documents_verified && updatedContracts[0].referrer_documents_verified,
        status: newStatus
      });
    } else {
      return res.status(400).json({ error: "Invalid action" });
    }
  } catch (error) {
    console.error("Failed to verify documents:", error);
    res.status(500).json({ error: "Failed to verify documents" });
  }
});
