import { withAuth } from "../../../lib/middleware";
import { query } from "../../../lib/db";
import { formatPercentage } from "../../../lib/utils";
import { PDFDocument, rgb, StandardFonts } from "pdf-lib";

export default withAuth(async (req, res) => {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  const { applicationId } = req.body;

  try {
    // Get application details
    const applications = await query(
      `
      SELECT 
        a.*,
        jp.title as job_title,
        jp.payment_type,
        jp.payment_percentage,
        jp.payment_fixed,
        seeker.full_name as seeker_name,
        seeker.email as seeker_email,
        referrer.full_name as referrer_name,
        referrer.email as referrer_email
      FROM applications a
      JOIN job_posts jp ON a.job_post_id = jp.id
      JOIN users seeker ON jp.user_id = seeker.id
      JOIN users referrer ON a.referrer_id = referrer.id
      WHERE a.id = ? AND a.status = 'accepted'
    `,
      [applicationId]
    );

    if (applications.length === 0) {
      return res
        .status(404)
        .json({ error: "Application not found or not accepted" });
    }

    const app = applications[0];

    // Create PDF
    const pdfDoc = await PDFDocument.create();
    const page = pdfDoc.addPage([600, 800]);
    const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
    const boldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold);

    const { width, height } = page.getSize();
    let y = height - 50;

    // Title
    page.drawText("REFERRAL AGREEMENT", {
      x: 200,
      y: y,
      size: 20,
      font: boldFont,
      color: rgb(0, 0, 0),
    });

    y -= 40;

    // Agreement content
    const content = [
      `Date: ${new Date().toLocaleDateString()}`,
      "",
      "PARTIES:",
      `Job Seeker: ${app.seeker_name} (${app.seeker_email})`,
      `Referrer: ${app.referrer_name} (${app.referrer_email})`,
      "",
      "JOB DETAILS:",
      `Position: ${app.job_title}`,
      `Company: ${app.company_name || "To be determined"}`,
      "",
      "COMPENSATION TERMS:",
      app.payment_type === "percentage"
        ? `${formatPercentage(app.payment_percentage)}% of monthly salary (recurring)`
        : `Fixed amount: ₹${app.payment_fixed}`,
      "",
      "TERMS AND CONDITIONS:",
      "1. The Referrer agrees to refer the Job Seeker for the specified position.",
      "2. The Job Seeker agrees to pay the agreed compensation upon successful placement.",
      "3. Payment shall be made within 30 days of joining the company.",
      "4. Both parties agree to maintain confidentiality.",
      "5. This agreement is binding upon successful placement.",
      "",
      "SIGNATURES:",
      "",
      "_____________________               _____________________",
      "Job Seeker                          Referrer",
    ];

    content.forEach((line) => {
      page.drawText(line, {
        x: 50,
        y: y,
        size: 12,
        font: line.includes(":") ? boldFont : font,
        color: rgb(0, 0, 0),
      });
      y -= 20;
    });

    // Generate PDF bytes
    const pdfBytes = await pdfDoc.save();

    // Save contract to database
    const result = await query(
      `INSERT INTO contracts (application_id, status) VALUES (?, 'pending_signatures')`,
      [applicationId]
    );

    // Return PDF
    res.setHeader("Content-Type", "application/pdf");
    res.setHeader(
      "Content-Disposition",
      `attachment; filename="contract-${applicationId}.pdf"`
    );
    res.status(200).send(Buffer.from(pdfBytes));
  } catch (error) {
    console.error("Failed to generate contract:", error);
    res.status(500).json({ error: "Failed to generate contract" });
  }
});
