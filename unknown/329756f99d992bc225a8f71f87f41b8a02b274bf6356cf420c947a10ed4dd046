import { useState, useRef } from "react";
import SignatureCanvas from "react-signature-canvas";
import { FileText, Download, Check } from "lucide-react";
import { formatPercentage } from "../lib/utils";

export default function ContractViewer({ contract, user, onSign }) {
  const [signing, setSigning] = useState(false);
  const [saving, setSaving] = useState(false);
  const sigCanvas = useRef();

  const clearSignature = () => {
    sigCanvas.current.clear();
  };

  const saveSignature = async () => {
    if (sigCanvas.current.isEmpty()) {
      alert("Please provide a signature");
      return;
    }

    setSaving(true);
    const signature = sigCanvas.current.toDataURL();

    try {
      const res = await fetch("/api/contracts/sign", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          contractId: contract.id,
          signature,
        }),
      });

      if (res.ok) {
        alert("Signature saved successfully!");
        onSign();
        setSigning(false);
        clearSignature();
      } else {
        const error = await res.json();
        console.error("Failed to save signature:", error);
        alert(error.error || "Failed to save signature");
      }
    } catch (error) {
      alert("Failed to save signature");
    } finally {
      setSaving(false);
    }
  };

  const isSeeker = user.user_type === "seeker";
  const hasSignedAlready = isSeeker
    ? contract.seeker_signed
    : contract.referrer_signed;
  const otherPartySigned = isSeeker
    ? contract.referrer_signed
    : contract.seeker_signed;

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold flex items-center gap-2">
          <FileText size={24} />
          Referral Agreement
        </h2>
        <button className="flex items-center gap-2 text-gray-600 hover:text-gray-800">
          <Download size={20} />
          Download PDF
        </button>
      </div>

      {/* Contract Status */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
        <h3 className="font-medium mb-2">Signature Status</h3>
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            {contract.seeker_signed ? (
              <Check className="text-green-500" size={20} />
            ) : (
              <div className="w-5 h-5 border-2 border-gray-300 rounded"></div>
            )}
            <span>
              Job Seeker: {contract.seeker_signed ? "Signed" : "Pending"}
            </span>
          </div>
          <div className="flex items-center gap-2">
            {contract.referrer_signed ? (
              <Check className="text-green-500" size={20} />
            ) : (
              <div className="w-5 h-5 border-2 border-gray-300 rounded"></div>
            )}
            <span>
              Referrer: {contract.referrer_signed ? "Signed" : "Pending"}
            </span>
          </div>
        </div>
      </div>

      {/* Contract Content */}
      <div className="mb-6 p-6 border rounded-lg">
        <h3 className="font-bold text-center mb-4">REFERRAL AGREEMENT</h3>

        <div className="space-y-4 text-sm">
          <p>
            <strong>Date:</strong>{" "}
            {new Date(contract.created_at).toLocaleDateString()}
          </p>

          <div>
            <p className="font-semibold mb-1">PARTIES:</p>
            <p>
              Job Seeker: {contract.seeker_name} ({contract.seeker_email})
            </p>
            <p>
              Referrer: {contract.referrer_name} ({contract.referrer_email})
            </p>
          </div>

          <div>
            <p className="font-semibold mb-1">JOB DETAILS:</p>
            <p>Position: {contract.job_title}</p>
            <p>Company: {contract.company_name || "To be determined"}</p>
          </div>

          <div>
            <p className="font-semibold mb-1">COMPENSATION TERMS:</p>
            <p>
              {contract.payment_type === "percentage"
                ? `${formatPercentage(contract.payment_percentage)}% of monthly salary (recurring)`
                : `Fixed amount: ₹${contract.payment_fixed}`}
            </p>
          </div>

          <div>
            <p className="font-semibold mb-1">TERMS AND CONDITIONS:</p>
            <ol className="list-decimal list-inside space-y-1">
              <li>
                The Referrer agrees to refer the Job Seeker for the specified
                position.
              </li>
              <li>
                The Job Seeker agrees to pay the agreed compensation upon
                successful placement.
              </li>
              <li>
                Payment shall be made within 30 days of joining the company.
              </li>
              <li>Both parties agree to maintain confidentiality.</li>
              <li>This agreement is binding upon successful placement.</li>
            </ol>
          </div>
        </div>
      </div>

      {/* Signature Section */}
      {!hasSignedAlready && (
        <div className="mt-6">
          {!signing ? (
            <button
              onClick={() => setSigning(true)}
              className="w-full py-3 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors"
            >
              Sign Contract
            </button>
          ) : (
            <div className="space-y-4">
              <h3 className="font-medium">Please sign below:</h3>
              <div className="border-2 border-gray-300 rounded">
                <SignatureCanvas
                  ref={sigCanvas}
                  canvasProps={{
                    className: "w-full h-40",
                  }}
                />
              </div>
              <div className="flex gap-4">
                <button
                  onClick={clearSignature}
                  className="flex-1 py-2 border border-gray-300 rounded hover:bg-gray-50"
                >
                  Clear
                </button>
                <button
                  onClick={() => setSigning(false)}
                  className="flex-1 py-2 border border-gray-300 rounded hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  onClick={saveSignature}
                  disabled={saving}
                  className="flex-1 py-2 bg-black text-white rounded hover:bg-gray-800 disabled:opacity-50"
                >
                  {saving ? "Saving..." : "Save Signature"}
                </button>
              </div>
            </div>
          )}
        </div>
      )}

      {hasSignedAlready && (
        <div className="mt-6 p-4 bg-green-50 rounded-lg text-green-800">
          <p className="flex items-center gap-2">
            <Check size={20} />
            You have already signed this contract.
          </p>
          {!otherPartySigned && (
            <p className="text-sm mt-1">Waiting for the other party to sign.</p>
          )}
        </div>
      )}

      {contract.status === "signed" && (
        <div className="mt-6 p-4 bg-blue-50 rounded-lg text-blue-800">
          <p className="font-medium">Contract fully executed!</p>
          <p className="text-sm mt-1">
            Both parties have signed. You can now proceed with document
            exchange.
          </p>
        </div>
      )}
    </div>
  );
}
