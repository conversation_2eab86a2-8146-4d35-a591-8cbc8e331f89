import { useState, useEffect } from "react";
import { useRouter } from "next/router";
import Layout from "../../components/Layout";
import ContractViewer from "../../components/ContractViewer";
import DocumentUpload from "../../components/DocumentUpload";
import ReviewForm from "../../components/ReviewForm";
import { FileText, Shield, Star } from "lucide-react";

export default function ContractPage({ user }) {
  const router = useRouter();
  const { id } = router.query;
  const [application, setApplication] = useState(null);
  const [contract, setContract] = useState(null);
  const [activeTab, setActiveTab] = useState("contract");
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (id && user) {
      fetchApplicationData();
    }
  }, [id, user]);

  const fetchApplicationData = async () => {
    try {
      // Fetch application details
      const appRes = await fetch(`/api/applications/${id}`);
      const appData = await appRes.json();
      setApplication(appData.application);

      // Fetch or generate contract
      if (appData.application.contract_id) {
        const contractRes = await fetch(
          `/api/contracts/${appData.application.contract_id}`
        );
        const contractData = await contractRes.json();
        setContract(contractData.contract);
      } else {
        // Generate new contract
        const contractRes = await fetch("/api/contracts/generate", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ applicationId: id }),
        });
        if (contractRes.ok) {
          const contractData = await contractRes.json();
          setContract(contractData.contract);
        }
      }
    } catch (error) {
      console.error("Failed to fetch data:", error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Layout user={user}>
        <div className="min-h-screen flex items-center justify-center">
          <div>Loading...</div>
        </div>
      </Layout>
    );
  }

  if (!application || !contract) {
    return (
      <Layout user={user}>
        <div className="min-h-screen flex items-center justify-center">
          <div>Application not found</div>
        </div>
      </Layout>
    );
  }

  const tabs = [
    { id: "contract", label: "Contract", icon: FileText },
    { id: "documents", label: "Documents", icon: Shield },
    { id: "review", label: "Review", icon: Star },
  ];

  const canAccessDocuments = contract.status === "signed";
  const canLeaveReview = application.status === "completed";

  return (
    <Layout user={user}>
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4">
          <h1 className="text-2xl font-bold mb-6">
            Application: {application.job_title}
          </h1>

          {/* Status Badge */}
          <div className="mb-6">
            <span
              className={`px-3 py-1 rounded-full text-sm font-medium ${
                application.status === "completed"
                  ? "bg-green-100 text-green-800"
                  : "bg-blue-100 text-blue-800"
              }`}
            >
              {application.status.replace("_", " ")}
            </span>
          </div>

          {/* Tabs */}
          <div className="bg-white rounded-lg shadow mb-6">
            <div className="border-b">
              <div className="flex">
                {tabs.map((tab) => {
                  const Icon = tab.icon;
                  const isDisabled =
                    (tab.id === "documents" && !canAccessDocuments) ||
                    (tab.id === "review" && !canLeaveReview);

                  return (
                    <button
                      key={tab.id}
                      onClick={() => !isDisabled && setActiveTab(tab.id)}
                      disabled={isDisabled}
                      className={`flex-1 px-4 py-4 font-medium flex items-center justify-center gap-2 transition-colors ${
                        activeTab === tab.id
                          ? "border-b-2 border-black text-black"
                          : isDisabled
                          ? "text-gray-300 cursor-not-allowed"
                          : "text-gray-500 hover:text-gray-700"
                      }`}
                    >
                      <Icon size={20} />
                      {tab.label}
                    </button>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Tab Content */}
          {activeTab === "contract" && (
            <ContractViewer
              contract={contract}
              user={user}
              onSign={fetchApplicationData}
            />
          )}

          {activeTab === "documents" && canAccessDocuments && (
            <DocumentUpload
              applicationId={application.id}
              onUploadComplete={fetchApplicationData}
            />
          )}

          {activeTab === "review" && canLeaveReview && (
            <ReviewForm
              applicationId={application.id}
              onSubmit={() => router.push("/dashboard")}
            />
          )}

          {/* Tab Instructions */}
          {activeTab === "documents" && !canAccessDocuments && (
            <div className="bg-white rounded-lg shadow p-8 text-center">
              <Shield size={48} className="mx-auto mb-4 text-gray-300" />
              <h3 className="text-lg font-medium mb-2">Contract Required</h3>
              <p className="text-gray-600">
                Both parties must sign the contract before proceeding with
                document exchange.
              </p>
            </div>
          )}

          {activeTab === "review" && !canLeaveReview && (
            <div className="bg-white rounded-lg shadow p-8 text-center">
              <Star size={48} className="mx-auto mb-4 text-gray-300" />
              <h3 className="text-lg font-medium mb-2">
                Transaction Not Complete
              </h3>
              <p className="text-gray-600">
                You can leave a review once the referral process is completed.
              </p>
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
}
