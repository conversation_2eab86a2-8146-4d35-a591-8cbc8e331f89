import { useState, useEffect } from "react";
import { useRouter } from "next/router";
import Layout from "../../components/Layout";
import ContractViewer from "../../components/ContractViewer";
import DocumentUpload from "../../components/DocumentUpload";
import DocumentReviewModal from "../../components/DocumentReviewModal";
import ReviewForm from "../../components/ReviewForm";
import { FileText, Shield, Star, CheckCircle, Download, Eye } from "lucide-react";

export default function ContractPage({ user }) {
  const router = useRouter();
  const { id } = router.query;
  const [application, setApplication] = useState(null);
  const [contract, setContract] = useState(null);
  const [activeTab, setActiveTab] = useState("contract");
  const [loading, setLoading] = useState(true);
  const [showDocumentReview, setShowDocumentReview] = useState(false);
  const [verifying, setVerifying] = useState(false);
  const [releasingOffer, setReleasingOffer] = useState(false);

  useEffect(() => {
    if (id && user) {
      fetchApplicationData();
    }
  }, [id, user]);

  const fetchApplicationData = async () => {
    try {
      // Fetch contract details (id is contract ID)
      const contractRes = await fetch(`/api/contracts/${id}`);

      if (contractRes.ok) {
        const contractData = await contractRes.json();
        setContract(contractData.contract);

        // Create application object from contract data for compatibility
        setApplication({
          id: contractData.contract.application_id,
          job_post_id: contractData.contract.job_post_id,
          referrer_id: contractData.contract.referrer_id,
          job_title: contractData.contract.job_title,
          company_name: contractData.contract.company_name,
          status: 'accepted', // If contract exists, application must be accepted
          contract_id: contractData.contract.id
        });
      } else {
        console.error("Failed to fetch contract");
        router.push('/dashboard');
      }
    } catch (error) {
      console.error("Failed to fetch data:", error);
      router.push('/dashboard');
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyDocuments = async () => {
    setVerifying(true);
    try {
      const res = await fetch("/api/contracts/verify-documents", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          contractId: contract.id,
          action: "verify"
        }),
      });

      if (res.ok) {
        const data = await res.json();
        alert(data.message);
        fetchApplicationData(); // Refresh contract data
      } else {
        const error = await res.json();
        alert(error.error || "Failed to verify documents");
      }
    } catch (error) {
      alert("Failed to verify documents");
      console.error("Failed to verify documents:", error);
    } finally {
      setVerifying(false);
    }
  };

  const handleReleaseOffer = async () => {
    const confirmed = window.confirm(
      "Are you sure you want to release the offer letter? This action cannot be undone."
    );

    if (!confirmed) return;

    setReleasingOffer(true);
    try {
      const res = await fetch("/api/contracts/release-offer", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ contractId: contract.id }),
      });

      if (res.ok) {
        const data = await res.json();
        alert("Offer letter released successfully!");

        // Open the offer letter
        window.open(data.offerLetterUrl, '_blank');

        fetchApplicationData(); // Refresh contract data
      } else {
        const error = await res.json();
        alert(error.error || "Failed to release offer letter");
      }
    } catch (error) {
      alert("Failed to release offer letter");
      console.error("Failed to release offer letter:", error);
    } finally {
      setReleasingOffer(false);
    }
  };

  if (loading) {
    return (
      <Layout user={user}>
        <div className="min-h-screen flex items-center justify-center">
          <div>Loading...</div>
        </div>
      </Layout>
    );
  }

  if (!application || !contract) {
    return (
      <Layout user={user}>
        <div className="min-h-screen flex items-center justify-center">
          <div>Application not found</div>
        </div>
      </Layout>
    );
  }

  const tabs = [
    { id: "contract", label: "Contract", icon: FileText },
    { id: "documents", label: "Documents", icon: Shield },
    { id: "review", label: "Review", icon: Star },
  ];

  const canAccessDocuments = contract.status === "signed" || contract.status === "documents_pending" || contract.status === "documents_verified" || contract.status === "offer_released";
  const canLeaveReview = application.status === "completed";
  const canVerifyDocuments = canAccessDocuments && !contract.seeker_documents_verified && !contract.referrer_documents_verified;
  const hasVerifiedDocuments = contract.seeker_documents_verified && contract.referrer_documents_verified;
  const canReleaseOffer = hasVerifiedDocuments && !contract.offer_letter_released;

  const isSeeker = user.user_type === "seeker";
  const hasUserVerified = isSeeker ? contract.seeker_documents_verified : contract.referrer_documents_verified;
  const otherPartyVerified = isSeeker ? contract.referrer_documents_verified : contract.seeker_documents_verified;

  return (
    <Layout user={user}>
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4">
          <h1 className="text-2xl font-bold mb-6">
            Application: {application.job_title}
          </h1>

          {/* Status Badge */}
          <div className="mb-6">
            <span
              className={`px-3 py-1 rounded-full text-sm font-medium ${
                application.status === "completed"
                  ? "bg-green-100 text-green-800"
                  : "bg-blue-100 text-blue-800"
              }`}
            >
              {application.status.replace("_", " ")}
            </span>
          </div>

          {/* Tabs */}
          <div className="bg-white rounded-lg shadow mb-6">
            <div className="border-b">
              <div className="flex">
                {tabs.map((tab) => {
                  const Icon = tab.icon;
                  const isDisabled =
                    (tab.id === "documents" && !canAccessDocuments) ||
                    (tab.id === "review" && !canLeaveReview);

                  return (
                    <button
                      key={tab.id}
                      onClick={() => !isDisabled && setActiveTab(tab.id)}
                      disabled={isDisabled}
                      className={`flex-1 px-4 py-4 font-medium flex items-center justify-center gap-2 transition-colors ${
                        activeTab === tab.id
                          ? "border-b-2 border-black text-black"
                          : isDisabled
                          ? "text-gray-300 cursor-not-allowed"
                          : "text-gray-500 hover:text-gray-700"
                      }`}
                    >
                      <Icon size={20} />
                      {tab.label}
                    </button>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Tab Content */}
          {activeTab === "contract" && (
            <ContractViewer
              contract={contract}
              user={user}
              onSign={fetchApplicationData}
            />
          )}

          {activeTab === "documents" && canAccessDocuments && (
            <div className="space-y-6">
              <DocumentUpload
                applicationId={application.id}
                onUploadComplete={fetchApplicationData}
              />

              {/* Document Verification Section */}
              {canAccessDocuments && (
                <div className="bg-white rounded-lg shadow p-6">
                  <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                    <CheckCircle size={20} />
                    Document Verification
                  </h3>

                  {/* Verification Status */}
                  <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                    <h4 className="font-medium mb-3">Verification Status</h4>
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        {contract.seeker_documents_verified ? (
                          <CheckCircle className="text-green-500" size={20} />
                        ) : (
                          <div className="w-5 h-5 border-2 border-gray-300 rounded-full"></div>
                        )}
                        <span>Job Seeker: {contract.seeker_documents_verified ? "Verified" : "Pending"}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        {contract.referrer_documents_verified ? (
                          <CheckCircle className="text-green-500" size={20} />
                        ) : (
                          <div className="w-5 h-5 border-2 border-gray-300 rounded-full"></div>
                        )}
                        <span>Referrer: {contract.referrer_documents_verified ? "Verified" : "Pending"}</span>
                      </div>
                    </div>
                  </div>

                  {/* Verification Actions */}
                  {!hasUserVerified && (
                    <div className="space-y-4">
                      <button
                        onClick={() => setShowDocumentReview(true)}
                        className="w-full py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-2"
                      >
                        <Eye size={20} />
                        Review & Verify Documents
                      </button>
                      <p className="text-sm text-gray-600 text-center">
                        Click to review all uploaded documents and verify their authenticity
                      </p>
                    </div>
                  )}

                  {hasUserVerified && !otherPartyVerified && (
                    <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                      <p className="text-green-800 flex items-center gap-2">
                        <CheckCircle size={20} />
                        You have verified the documents. Waiting for the other party to verify.
                      </p>
                    </div>
                  )}

                  {hasVerifiedDocuments && (
                    <div className="space-y-4">
                      <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                        <p className="text-green-800 font-medium flex items-center gap-2">
                          <CheckCircle size={20} />
                          All documents verified by both parties!
                        </p>
                      </div>

                      {canReleaseOffer && (
                        <button
                          onClick={handleReleaseOffer}
                          disabled={releasingOffer}
                          className="w-full py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 transition-colors flex items-center justify-center gap-2"
                        >
                          {releasingOffer ? (
                            <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                          ) : (
                            <Download size={20} />
                          )}
                          {releasingOffer ? "Releasing..." : "Release Offer Letter"}
                        </button>
                      )}

                      {contract.offer_letter_released && (
                        <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                          <p className="text-blue-800 font-medium mb-2">🎉 Offer Letter Released!</p>
                          <button
                            onClick={() => window.open(contract.offer_letter_url, '_blank')}
                            className="flex items-center gap-2 text-blue-600 hover:text-blue-800"
                          >
                            <Download size={16} />
                            Download Offer Letter
                          </button>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )}
            </div>
          )}

          {activeTab === "review" && canLeaveReview && (
            <ReviewForm
              applicationId={application.id}
              onSubmit={() => router.push("/dashboard")}
            />
          )}

          {/* Tab Instructions */}
          {activeTab === "documents" && !canAccessDocuments && (
            <div className="bg-white rounded-lg shadow p-8 text-center">
              <Shield size={48} className="mx-auto mb-4 text-gray-300" />
              <h3 className="text-lg font-medium mb-2">Contract Required</h3>
              <p className="text-gray-600">
                Both parties must sign the contract before proceeding with
                document exchange.
              </p>
            </div>
          )}

          {activeTab === "review" && !canLeaveReview && (
            <div className="bg-white rounded-lg shadow p-8 text-center">
              <Star size={48} className="mx-auto mb-4 text-gray-300" />
              <h3 className="text-lg font-medium mb-2">
                Transaction Not Complete
              </h3>
              <p className="text-gray-600">
                You can leave a review once the referral process is completed.
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Document Review Modal */}
      <DocumentReviewModal
        isOpen={showDocumentReview}
        onClose={() => setShowDocumentReview(false)}
        applicationId={application?.id}
        onVerify={handleVerifyDocuments}
        currentUser={user}
      />
    </Layout>
  );
}
