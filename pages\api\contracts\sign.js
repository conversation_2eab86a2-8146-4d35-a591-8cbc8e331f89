import { withAuth } from "../../../lib/middleware";
import { query } from "../../../lib/db";

export default withAuth(async (req, res) => {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  const { contractId, signature } = req.body;

  try {
    // Get contract details
    const contracts = await query(
      `
      SELECT 
        c.*,
        a.referrer_id,
        jp.user_id as seeker_id
      FROM contracts c
      JOIN applications a ON c.application_id = a.id
      JOIN job_posts jp ON a.job_post_id = jp.id
      WHERE c.id = ?
    `,
      [contractId]
    );

    if (contracts.length === 0) {
      return res.status(404).json({ error: "Contract not found" });
    }

    const contract = contracts[0];

    // Determine which party is signing
    let updateField;
    if (req.user.id === contract.seeker_id) {
      updateField = "seeker";
    } else if (req.user.id === contract.referrer_id) {
      updateField = "referrer";
    } else {
      return res
        .status(403)
        .json({ error: "Unauthorized to sign this contract" });
    }

    // Update signature
    await query(
      `UPDATE contracts 
       SET ${updateField}_signature = ?, ${updateField}_signed_at = NOW() 
       WHERE id = ?`,
      [signature, contractId]
    );

    // Check if both parties have signed
    const updatedContracts = await query(
      "SELECT seeker_signed, referrer_signed FROM contracts WHERE id = ?",
      [contractId]
    );

    if (
      updatedContracts[0].seeker_signed &&
      updatedContracts[0].referrer_signed
    ) {
      // Update contract status
      await query('UPDATE contracts SET status = "signed" WHERE id = ?', [
        contractId,
      ]);

      // Update application status
      await query(
        'UPDATE applications SET status = "in_progress" WHERE id = ?',
        [contract.application_id]
      );
    }

    res.status(200).json({ success: true });
  } catch (error) {
    console.error("Failed to sign contract:", error);
    res.status(500).json({ error: "Failed to sign contract" });
  }
});
