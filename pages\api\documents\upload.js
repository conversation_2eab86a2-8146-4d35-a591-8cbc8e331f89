import { withAuth } from "../../../lib/middleware";
import { query } from "../../../lib/db";
import formidable from "formidable";
import fs from "fs";
import path from "path";

export const config = {
  api: {
    bodyParser: false,
  },
};

export default withAuth(async (req, res) => {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  // Create upload directory if it doesn't exist
  const uploadDir = "./public/uploads";
  if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
  }

  const form = new formidable.IncomingForm({
    uploadDir: uploadDir,
    keepExtensions: true,
    maxFileSize: 5 * 1024 * 1024, // 5MB
    multiples: false,
  });

  try {
    const [fields, files] = await form.parse(req);

    console.log("Parsed fields:", fields);
    console.log("Parsed files:", files);

    // Extract fields (formidable v3 returns arrays)
    const applicationId = Array.isArray(fields.applicationId) ? fields.applicationId[0] : fields.applicationId;
    const documentType = Array.isArray(fields.documentType) ? fields.documentType[0] : fields.documentType;

    // Extract file (formidable v3 returns arrays)
    const file = Array.isArray(files.document) ? files.document[0] : files.document;

    if (!file) {
      return res.status(400).json({ error: "No file uploaded" });
    }

    if (!applicationId || !documentType) {
      return res.status(400).json({ error: "Missing applicationId or documentType" });
    }

    // Validate document type
    const validDocumentTypes = ['aadhar', 'pan', 'driving_license', 'passport'];
    if (!validDocumentTypes.includes(documentType)) {
      return res.status(400).json({ error: "Invalid document type" });
    }

    console.log("Processing file:", {
      originalFilename: file.originalFilename,
      filepath: file.filepath,
      size: file.size,
      mimetype: file.mimetype
    });

    // Validate file type
    const allowedTypes = [
      "image/jpeg",
      "image/png",
      "image/jpg",
      "application/pdf",
    ];

    if (!allowedTypes.includes(file.mimetype)) {
      // Clean up uploaded file
      if (fs.existsSync(file.filepath)) {
        fs.unlinkSync(file.filepath);
      }
      return res.status(400).json({ error: "Invalid file type. Please upload JPG, PNG, or PDF files." });
    }

    // Generate unique filename
    const ext = path.extname(file.originalFilename || '');
    const filename = `${Date.now()}-${Math.random()
      .toString(36)
      .substring(7)}${ext}`;
    const newPath = path.join(uploadDir, filename);

    // Move file to uploads directory
    fs.renameSync(file.filepath, newPath);
    console.log("File moved to:", newPath);

    // Save to database
    await query(
      `INSERT INTO documents (
        user_id, application_id, document_type, document_url
      ) VALUES (?, ?, ?, ?)`,
      [req.user.id, applicationId, documentType, `/uploads/${filename}`]
    );

    console.log("Document saved to database");

    res.status(200).json({
      url: `/uploads/${filename}`,
      message: "Document uploaded successfully"
    });
  } catch (error) {
    console.error("Failed to save document:", error);
    res.status(500).json({
      error: "Failed to save document",
      details: error.message
    });
  }
});
