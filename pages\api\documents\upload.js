import { withAuth } from "../../../lib/middleware";
import { query } from "../../../lib/db";
import formidable from "formidable";
import fs from "fs";
import path from "path";

export const config = {
  api: {
    bodyParser: false,
  },
};

export default withAuth(async (req, res) => {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  const form = new formidable.IncomingForm({
    uploadDir: "./public/uploads",
    keepExtensions: true,
    maxFileSize: 5 * 1024 * 1024, // 5MB
  });

  // Create upload directory if it doesn't exist
  const uploadDir = "./public/uploads";
  if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
  }

  form.parse(req, async (err, fields, files) => {
    if (err) {
      return res.status(400).json({ error: "Failed to upload file" });
    }

    try {
      const { applicationId, documentType } = fields;
      const file = files.document;

      if (!file) {
        return res.status(400).json({ error: "No file uploaded" });
      }

      // Generate unique filename
      const ext = path.extname(file.originalFilename);
      const filename = `${Date.now()}-${Math.random()
        .toString(36)
        .substring(7)}${ext}`;
      const newPath = path.join(uploadDir, filename);

      // Move file to uploads directory
      fs.renameSync(file.filepath, newPath);

      // Save to database
      await query(
        `INSERT INTO documents (
          user_id, application_id, document_type, document_url
        ) VALUES (?, ?, ?, ?)`,
        [req.user.id, applicationId, documentType, `/uploads/${filename}`]
      );

      res.status(200).json({ url: `/uploads/${filename}` });
    } catch (error) {
      console.error("Failed to save document:", error);
      res.status(500).json({ error: "Failed to save document" });
    }
  });
});
