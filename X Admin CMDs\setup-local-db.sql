-- Create local database for SplitJob development
CREATE DATABASE IF NOT EXISTS splitjob_local;
USE splitjob_local;

-- Create users table
CREATE TABLE IF NOT EXISTS users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  user_type <PERSON><PERSON><PERSON>('job_seeker', 'employer') NOT NULL,
  full_name VARCHAR(255) NOT NULL,
  phone VARCHAR(20),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create job_posts table
CREATE TABLE IF NOT EXISTS job_posts (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  title VARCHAR(255) NOT NULL,
  description TEXT NOT NULL,
  location VARCHAR(255),
  salary_min DECIMAL(10,2),
  salary_max DECIMAL(10,2),
  job_type ENUM('full_time', 'part_time', 'contract', 'freelance') NOT NULL,
  status ENUM('active', 'inactive', 'closed') DEFAULT 'active',
  visibility ENUM('public', 'private', 'anonymous') DEFAULT 'public',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create applications table
CREATE TABLE IF NOT EXISTS applications (
  id INT AUTO_INCREMENT PRIMARY KEY,
  job_post_id INT NOT NULL,
  user_id INT NOT NULL,
  status ENUM('pending', 'accepted', 'rejected') DEFAULT 'pending',
  cover_letter TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (job_post_id) REFERENCES job_posts(id) ON DELETE CASCADE,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  UNIQUE KEY unique_application (job_post_id, user_id)
);

-- Insert some sample data for testing
INSERT IGNORE INTO users (email, password_hash, user_type, full_name, phone) VALUES
('<EMAIL>', '$2a$10$example.hash.here', 'employer', 'Test Employer', '+1234567890'),
('<EMAIL>', '$2a$10$example.hash.here', 'job_seeker', 'Test Job Seeker', '+0987654321');

INSERT IGNORE INTO job_posts (user_id, title, description, location, salary_min, salary_max, job_type, status, visibility) VALUES
(1, 'Software Developer', 'Looking for a skilled software developer to join our team.', 'Remote', 50000, 80000, 'full_time', 'active', 'public'),
(1, 'Frontend Developer', 'Need a frontend developer with React experience.', 'New York', 60000, 90000, 'full_time', 'active', 'public');

SELECT 'Database setup completed successfully!' as message;
