-- Create database
CREATE DATABASE IF NOT EXISTS split_job;
USE split_job;

-- Users table
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    user_type ENU<PERSON>('seeker', 'referrer') NOT NULL,
    full_name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    phone VARCHAR(20),
    linkedin_url VARCHAR(500),
    github_url VARCHAR(500),
    portfolio_url VARCHAR(500),
    resume_url VARCHAR(500),
    profile_picture VARCHAR(500),
    bio TEXT,
    is_verified BOOLEAN DEFAULT FALSE,
    email_verified BO<PERSON>EA<PERSON> DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Job posts table
CREATE TABLE job_posts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    job_role VARCHAR(100) NOT NULL,
    skills TEXT,
    experience_years INT,
    desired_companies TEXT,
    payment_type ENUM('percentage', 'fixed') NOT NULL,
    payment_percentage DECIMAL(5,2),
    payment_fixed DECIMAL(10,2),
    visibility ENUM('public', 'private', 'anonymous') DEFAULT 'public',
    shareable_link VARCHAR(100) UNIQUE,
    status ENUM('active', 'closed', 'paused') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Applications table
CREATE TABLE applications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    job_post_id INT NOT NULL,
    referrer_id INT NOT NULL,
    message TEXT,
    company_name VARCHAR(255),
    position_details TEXT,
    status ENUM('pending', 'accepted', 'rejected', 'in_progress', 'completed') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (job_post_id) REFERENCES job_posts(id) ON DELETE CASCADE,
    FOREIGN KEY (referrer_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Contracts table
CREATE TABLE contracts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    application_id INT NOT NULL,
    contract_pdf_url VARCHAR(500),
    seeker_signed BOOLEAN DEFAULT FALSE,
    referrer_signed BOOLEAN DEFAULT FALSE,
    seeker_signature TEXT,
    referrer_signature TEXT,
    seeker_signed_at TIMESTAMP NULL,
    referrer_signed_at TIMESTAMP NULL,
    status ENUM('draft', 'pending_signatures', 'signed', 'cancelled') DEFAULT 'draft',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (application_id) REFERENCES applications(id) ON DELETE CASCADE
);

-- Documents table
CREATE TABLE documents (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    application_id INT NOT NULL,
    document_type ENUM('aadhar', 'pan', 'driving_license', 'passport') NOT NULL,
    document_url VARCHAR(500) NOT NULL,
    is_verified BOOLEAN DEFAULT FALSE,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (application_id) REFERENCES applications(id) ON DELETE CASCADE
);

-- Reviews table
CREATE TABLE reviews (
    id INT PRIMARY KEY AUTO_INCREMENT,
    application_id INT NOT NULL,
    reviewer_id INT NOT NULL,
    reviewed_user_id INT NOT NULL,
    rating INT CHECK (rating >= 1 AND rating <= 5),
    review_text TEXT,
    is_verified_transaction BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (application_id) REFERENCES applications(id) ON DELETE CASCADE,
    FOREIGN KEY (reviewer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (reviewed_user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_review (application_id, reviewer_id, reviewed_user_id)
);

-- Notifications table
CREATE TABLE notifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    type VARCHAR(50) NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT,
    is_read BOOLEAN DEFAULT FALSE,
    related_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Password reset tokens
CREATE TABLE password_resets (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Admin users table
CREATE TABLE admin_users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    role ENUM('super_admin', 'moderator') DEFAULT 'moderator',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Flagged content table
CREATE TABLE flagged_content (
    id INT PRIMARY KEY AUTO_INCREMENT,
    content_type ENUM('job_post', 'review', 'user') NOT NULL,
    content_id INT NOT NULL,
    flagged_by INT NOT NULL,
    reason TEXT,
    status ENUM('pending', 'resolved', 'dismissed') DEFAULT 'pending',
    admin_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    resolved_at TIMESTAMP NULL,
    FOREIGN KEY (flagged_by) REFERENCES users(id) ON DELETE CASCADE
);

-- Analytics table
CREATE TABLE analytics (
    id INT PRIMARY KEY AUTO_INCREMENT,
    event_type VARCHAR(50) NOT NULL,
    user_id INT,
    event_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Create indexes for better performance
CREATE INDEX idx_job_posts_user ON job_posts(user_id);
CREATE INDEX idx_job_posts_status ON job_posts(status);
CREATE INDEX idx_job_posts_visibility ON job_posts(visibility);
CREATE INDEX idx_applications_status ON applications(status);
CREATE INDEX idx_notifications_user ON notifications(user_id, is_read);
CREATE INDEX idx_reviews_user ON reviews(reviewed_user_id);
CREATE INDEX idx_analytics_event ON analytics(event_type, created_at);

-- Insert default admin user (change password immediately)
INSERT INTO admin_users (email, password_hash, full_name, role) 
VALUES ('<EMAIL>', '$2b$10$YourHashedPasswordHere', 'Admin User', 'super_admin');

-- Create views for easier querying
CREATE VIEW user_ratings AS
SELECT 
    u.id as user_id,
    u.full_name,
    u.user_type,
    COUNT(r.id) as total_reviews,
    AVG(r.rating) as average_rating
FROM users u
LEFT JOIN reviews r ON u.id = r.reviewed_user_id
GROUP BY u.id;

CREATE VIEW job_post_stats AS
SELECT 
    jp.id as job_post_id,
    jp.title,
    jp.user_id,
    COUNT(DISTINCT a.id) as total_applications,
    COUNT(DISTINCT CASE WHEN a.status = 'completed' THEN a.id END) as completed_referrals
FROM job_posts jp
LEFT JOIN applications a ON jp.id = a.job_post_id
GROUP BY jp.id;