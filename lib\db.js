const mysql = require("mysql2/promise");

let pool;

export async function getConnection() {
  if (!pool) {
    try {
      console.log('Creating database connection pool...');
      console.log('Host:', process.env.DB_HOST);
      console.log('User:', process.env.DB_USER);
      console.log('Database:', process.env.DB_NAME);
      console.log('Password length:', process.env.DB_PASSWORD?.length);

      const isLocalhost = process.env.DB_HOST === 'localhost' || process.env.DB_HOST === '127.0.0.1';

      const config = {
        host: process.env.DB_HOST,
        port: 3306,
        user: process.env.DB_USER,
        password: process.env.DB_PASSWORD || '',
        database: process.env.DB_NAME,
        waitForConnections: true,
        connectionLimit: 10,
        queueLimit: 0,
        charset: 'utf8mb4'
      };

      // Only add SSL for remote connections
      if (!isLocalhost) {
        config.ssl = {
          rejectUnauthorized: false
        };
      }

      console.log('Connecting to database:', {
        ...config,
        password: config.password ? '***' : '(empty)'
      });

      pool = mysql.createPool(config);

      // Test the connection
      const testConnection = await pool.getConnection();
      testConnection.release();
      console.log('✅ Database connected successfully');

    } catch (error) {
      console.error('❌ Database connection failed:', error);
      console.error('Error details:', {
        code: error.code,
        errno: error.errno,
        sqlState: error.sqlState,
        sqlMessage: error.sqlMessage
      });
      throw error;
    }
  }
  return pool;
}

export async function query(sql, params) {
  try {
    const connection = await getConnection();
    const [results] = await connection.execute(sql, params);
    return results;
  } catch (error) {
    console.error('Database query error:', error);
    console.error('SQL:', sql);
    console.error('Params:', params);
    throw error;
  }
}
