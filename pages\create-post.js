import { useState } from "react";
import { useRouter } from "next/router";
import Layout from "../components/Layout";
import { withUserType } from "../lib/middleware";

export default function CreatePost({ user }) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    jobRole: "software_developer",
    skills: "",
    experienceYears: "",
    desiredCompanies: "",
    paymentType: "percentage",
    paymentPercentage: "",
    paymentFixed: "",
    visibility: "public",
  });

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const res = await fetch("/api/jobs/create", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(formData),
      });

      if (res.ok) {
        const data = await res.json();
        router.push(`/job/${data.id}`);
      } else {
        alert("Failed to create job post");
      }
    } catch (error) {
      alert("Failed to create job post");
    } finally {
      setLoading(false);
    }
  };

  if (!user || user.user_type !== "seeker") {
    router.push("/dashboard");
    return null;
  }

  return (
    <Layout user={user}>
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-2xl mx-auto px-4">
          <div className="bg-white rounded-lg shadow-lg p-8">
            <h1 className="text-2xl font-bold mb-6">Create Job Post</h1>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label className="block text-sm font-medium mb-2">Title</label>
                <input
                  type="text"
                  required
                  value={formData.title}
                  onChange={(e) =>
                    setFormData({ ...formData, title: e.target.value })
                  }
                  className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                  style={{ backgroundColor: '#F9FAFB' }}
                  placeholder="e.g., Senior Software Developer needed at top tech companies"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  Description
                </label>
                <textarea
                  required
                  rows={6}
                  value={formData.description}
                  onChange={(e) =>
                    setFormData({ ...formData, description: e.target.value })
                  }
                  className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                  style={{ backgroundColor: '#F9FAFB' }}
                  placeholder="Describe your background, what kind of role you're looking for, and why referrers should help you..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  Job Role
                </label>
                <select
                  value={formData.jobRole}
                  onChange={(e) =>
                    setFormData({ ...formData, jobRole: e.target.value })
                  }
                  className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                  style={{ backgroundColor: '#F9FAFB' }}
                >
                  <option value="software_developer">Software Developer</option>
                  <option value="designer">Designer</option>
                  <option value="product_manager">Product Manager</option>
                  <option value="data_scientist">Data Scientist</option>
                  <option value="marketing">Marketing</option>
                  <option value="sales">Sales</option>
                  <option value="other">Other</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Skills</label>
                <input
                  type="text"
                  value={formData.skills}
                  onChange={(e) =>
                    setFormData({ ...formData, skills: e.target.value })
                  }
                  className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                  style={{ backgroundColor: '#F9FAFB' }}
                  placeholder="e.g., React, Node.js, Python, AWS"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  Years of Experience
                </label>
                <input
                  type="number"
                  value={formData.experienceYears}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      experienceYears: e.target.value,
                    })
                  }
                  className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                  style={{ backgroundColor: '#F9FAFB' }}
                  min="0"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  Target Companies (optional)
                </label>
                <input
                  type="text"
                  value={formData.desiredCompanies}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      desiredCompanies: e.target.value,
                    })
                  }
                  className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                  style={{ backgroundColor: '#F9FAFB' }}
                  placeholder="e.g., Google, Microsoft, Amazon"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  Payment Type
                </label>
                <div className="space-y-2">
                  <label className="flex items-center gap-2">
                    <input
                      type="radio"
                      name="paymentType"
                      value="fixed"
                      checked={formData.paymentType === "fixed"}
                      onChange={(e) =>
                        setFormData({
                          ...formData,
                          paymentType: e.target.value,
                        })
                      }
                    />
                    <span>Fixed amount (one-time)</span>
                  </label>
                </div>
              </div>

              {formData.paymentType === "percentage" && (
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Percentage of Monthly Salary
                  </label>
                  <input
                    type="number"
                    required
                    value={formData.paymentPercentage}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        paymentPercentage: e.target.value,
                      })
                    }
                    className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                    style={{ backgroundColor: '#F9FAFB' }}
                    min="1"
                    max="50"
                    placeholder="e.g., 10"
                  />
                  <p className="text-sm text-gray-600 mt-1">
                    You'll pay this percentage of your monthly salary as long as
                    you work at the company
                  </p>
                </div>
              )}

              {formData.paymentType === "fixed" && (
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Fixed Amount (₹)
                  </label>
                  <input
                    type="number"
                    required
                    value={formData.paymentFixed}
                    onChange={(e) =>
                      setFormData({ ...formData, paymentFixed: e.target.value })
                    }
                    className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                    style={{ backgroundColor: '#F9FAFB' }}
                    min="1000"
                    placeholder="e.g., 50000"
                  />
                  <p className="text-sm text-gray-600 mt-1">
                    One-time payment after successful placement
                  </p>
                </div>
              )}

              <div>
                <label className="block text-sm font-medium mb-2">
                  Visibility
                </label>
                <div className="space-y-2">
                  <label className="flex items-start gap-2">
                    <input
                      type="radio"
                      name="visibility"
                      value="public"
                      checked={formData.visibility === "public"}
                      onChange={(e) =>
                        setFormData({ ...formData, visibility: e.target.value })
                      }
                      className="mt-1"
                    />
                    <div>
                      <span className="font-medium">Public</span>
                      <p className="text-sm text-gray-600">
                        Visible to everyone on the explore page
                      </p>
                    </div>
                  </label>
                  <label className="flex items-start gap-2">
                    <input
                      type="radio"
                      name="visibility"
                      value="anonymous"
                      checked={formData.visibility === "anonymous"}
                      onChange={(e) =>
                        setFormData({ ...formData, visibility: e.target.value })
                      }
                      className="mt-1"
                    />
                    <div>
                      <span className="font-medium">Anonymous</span>
                      <p className="text-sm text-gray-600">
                        Your name will be hidden on the explore page
                      </p>
                    </div>
                  </label>
                  <label className="flex items-start gap-2">
                    <input
                      type="radio"
                      name="visibility"
                      value="private"
                      checked={formData.visibility === "private"}
                      onChange={(e) =>
                        setFormData({ ...formData, visibility: e.target.value })
                      }
                      className="mt-1"
                    />
                    <div>
                      <span className="font-medium">Private</span>
                      <p className="text-sm text-gray-600">
                        Only accessible via shareable link
                      </p>
                    </div>
                  </label>
                </div>
              </div>

              <button
                type="submit"
                disabled={loading}
                className="w-full py-3 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors disabled:opacity-50"
              >
                {loading ? "Creating..." : "Create Post"}
              </button>
            </form>
          </div>
        </div>
      </div>
    </Layout>
  );
}
